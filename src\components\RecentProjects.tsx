import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Play, X } from "lucide-react";
import { useState } from "react";

const RecentProjects = () => {
  // Video Player State Management
  // Reason for State: Control which video is currently playing in embedded player
  // Task Performed: Manages video player visibility and current video data
  // Linking Information: Internal - Used by video player modal and play buttons
  const [playingVideo, setPlayingVideo] = useState<{
    videoId: string;
    title: string;
  } | null>(null);

  // Real Video Projects Data with Mixed Platforms
  // Reason for Data: Store actual video information for success stories across different platforms
  // Task Performed: Defines real project data with YouTube video links but displayed as different platforms
  // Linking Information: External - Links to actual YouTube videos, Internal - Used by project cards
  const projects = [
    {
      id: 1,
      title: "Epic Gaming Montage",
      platform: "Instagram",
      category: "Gaming",
      videoId: "FSsSQkMzTzA",
      videoUrl: "https://www.youtube.com/shorts/FSsSQkMzTzA",
      image: "https://img.youtube.com/vi/FSsSQkMzTzA/maxresdefault.jpg",
      duration: "60s",
      views: "500K",
      engagement: "12.4%",
      description: "High-energy gaming montage with dynamic transitions and epic moments."
    },
    {
      id: 2,
      title: "Creative Content Showcase",
      platform: "Instagram",
      category: "Creative",
      videoId: "Y7xWZfu1o2U",
      videoUrl: "https://www.youtube.com/shorts/Y7xWZfu1o2U",
      image: "https://img.youtube.com/vi/Y7xWZfu1o2U/maxresdefault.jpg",
      duration: "45s",
      views: "300K",
      engagement: "15.7%",
      description: "Innovative creative content with stunning visual effects and storytelling."
    },
    {
      id: 3,
      title: "Viral Trend Video",
      platform: "Pinterest",
      category: "Trending",
      videoId: "DDRbI0X2IWE",
      videoUrl: "https://www.youtube.com/shorts/DDRbI0X2IWE",
      image: "https://img.youtube.com/vi/DDRbI0X2IWE/maxresdefault.jpg",
      duration: "30s",
      views: "400K",
      engagement: "18.2%",
      description: "Trending content that captures current viral moments with perfect timing."
    },
    {
      id: 4,
      title: "Professional Brand Story",
      platform: "Pinterest",
      category: "Business",
      videoId: "bDZV6_-Ksjc",
      videoUrl: "https://www.youtube.com/shorts/bDZV6_-Ksjc",
      image: "https://img.youtube.com/vi/bDZV6_-Ksjc/maxresdefault.jpg",
      duration: "60s",
      views: "250K",
      engagement: "9.8%",
      description: "Compelling brand storytelling with professional cinematography and messaging."
    },
    {
      id: 5,
      title: "Entertainment Highlight Reel",
      platform: "TikTok",
      category: "Entertainment",
      videoId: "2h8fZlyUdpY",
      videoUrl: "https://www.youtube.com/shorts/2h8fZlyUdpY",
      image: "https://img.youtube.com/vi/2h8fZlyUdpY/maxresdefault.jpg",
      duration: "45s",
      views: "350K",
      engagement: "14.3%",
      description: "Entertaining content with humor and engaging visual storytelling."
    },
    {
      id: 6,
      title: "Lifestyle & Inspiration",
      platform: "YouTube",
      category: "Lifestyle",
      videoId: "OsJbEIkABNs",
      videoUrl: "https://www.youtube.com/shorts/OsJbEIkABNs",
      image: "https://img.youtube.com/vi/OsJbEIkABNs/maxresdefault.jpg",
      duration: "30s",
      views: "200K",
      engagement: "11.6%",
      description: "Inspirational lifestyle content with motivational messaging and aesthetics."
    }
  ];

  // Platform Color Handler
  // Reason for Function: Assign platform-specific colors to badges
  // Task Performed: Returns appropriate CSS classes for platform badges
  // Linking Information: Internal - Used by platform badges in project cards
  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case "Instagram": return "bg-pink-500/10 text-pink-400 border-pink-500/20";
      case "YouTube":
      case "YouTube Shorts": return "bg-red-500/10 text-red-400 border-red-500/20";
      case "TikTok": return "bg-purple-500/10 text-purple-400 border-purple-500/20";
      case "Pinterest": return "bg-orange-500/10 text-orange-400 border-orange-500/20";
      default: return "bg-accent/10 text-accent border-accent/20";
    }
  };

  // Video Play Handler
  // Reason for Function: Handle play button clicks to start embedded video playback
  // Task Performed: Sets the current playing video and opens embedded player
  // Linking Information: Internal - Used by play buttons on project cards
  const handlePlayVideo = (videoId: string, title: string) => {
    setPlayingVideo({ videoId, title });
  };

  // Video Close Handler
  // Reason for Function: Handle closing the embedded video player
  // Task Performed: Closes the video player modal and stops playback
  // Linking Information: Internal - Used by close button in video player modal
  const handleCloseVideo = () => {
    setPlayingVideo(null);
  };



  return (
    <section id="projects" className="py-20 bg-background relative overflow-hidden">
      {/* Premium Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-secondary/30 to-background"></div>
      <div className="absolute top-1/4 left-0 w-72 h-72 bg-gradient-start/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-0 w-72 h-72 bg-gradient-end/10 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 lg:px-8 relative z-10">
        <div className="text-center mb-20">
          <Badge className="bg-hero-gradient text-white border-0 mb-6 px-6 py-3 text-sm font-semibold tracking-wide">
            🎬 Recent Projects
          </Badge>
          <h2 className="text-4xl md:text-6xl font-bold mb-8 leading-tight">
            Our Latest
            <span className="bg-hero-gradient bg-clip-text text-transparent block mt-2">
              Success Stories
            </span>
          </h2>
          <p className="text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            See how we've helped brands and creators achieve viral success with
            engaging short-form video content across all major platforms.
          </p>

          {/* Premium Divider */}
          <div className="flex items-center justify-center mt-8">
            <div className="h-px bg-gradient-to-r from-transparent via-accent to-transparent w-32"></div>
            <div className="mx-4 w-2 h-2 bg-accent rounded-full animate-pulse"></div>
            <div className="h-px bg-gradient-to-r from-transparent via-accent to-transparent w-32"></div>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {projects.map((project) => (
            <Card
              key={project.id}
              className="bg-card-gradient border-service-border hover:border-accent/30 transition-all duration-500 hover:scale-105 group overflow-hidden relative"
            >
              {/* Premium Card Background Effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-accent/5 via-transparent to-gradient-start/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <div className="relative">
                {/* Enhanced Image Container */}
                <div className="relative overflow-hidden">
                  <img
                    src={project.image}
                    alt={project.title}
                    className="w-full h-48 object-cover transition-transform duration-700 group-hover:scale-110"
                  />

                  {/* Premium Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-60"></div>

                  {/* Play Button Overlay */}
                  <div
                    className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500 cursor-pointer"
                    onClick={() => handlePlayVideo(project.videoId, project.title)}
                  >
                    <div className="bg-white/95 backdrop-blur-sm rounded-full p-4 group-hover:scale-110 transition-transform duration-300 shadow-2xl">
                      <Play className="h-6 w-6 text-primary ml-1" fill="currentColor" />
                    </div>
                  </div>

                  {/* Premium Corner Accent */}
                  <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-accent/20 to-transparent"></div>

                  <div className="absolute top-4 left-4">
                    <Badge className={`${getPlatformColor(project.platform)} shadow-lg backdrop-blur-sm`}>
                      {project.platform}
                    </Badge>
                  </div>
                  <div className="absolute top-4 right-4">
                    <Badge className="bg-black/70 text-white border-0 backdrop-blur-sm shadow-lg">
                      {project.duration}
                    </Badge>
                  </div>
                </div>
              </div>

                {/* Enhanced Card Content */}
                <CardContent className="p-8 relative z-10">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-bold mb-3 text-foreground group-hover:text-accent transition-colors duration-300">
                        {project.title}
                      </h3>
                      <p className="text-muted-foreground leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">
                        {project.description}
                      </p>
                    </div>

                    {/* Premium Stats Grid */}
                    <div className="grid grid-cols-2 gap-6">
                      <div className="text-center p-4 bg-gradient-to-br from-accent/10 to-transparent rounded-xl border border-accent/20 group-hover:border-accent/40 transition-colors duration-300">
                        <p className="text-2xl font-bold text-accent mb-1">
                          {project.views}
                        </p>
                        <p className="text-xs text-muted-foreground font-medium">Views</p>
                      </div>
                      <div className="text-center p-4 bg-gradient-to-br from-gradient-start/10 to-transparent rounded-xl border border-gradient-start/20 group-hover:border-gradient-start/40 transition-colors duration-300">
                        <p className="text-2xl font-bold text-gradient-start mb-1">
                          {project.engagement}
                        </p>
                        <p className="text-xs text-muted-foreground font-medium">Engagement</p>
                      </div>
                    </div>

                    {/* Premium Action Buttons */}
                    <div className="flex gap-2">
                      <Button
                        onClick={() => handlePlayVideo(project.videoId, project.title)}
                        className="w-full bg-hero-gradient hover:opacity-90 transition-all duration-300"
                      >
                        <Play size={16} className="mr-2" />
                        Play Video
                      </Button>
                    </div>
                  </div>

                  {/* Premium Bottom Accent */}
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-accent/50 via-gradient-start/50 to-accent/50 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-center"></div>
                </CardContent>
              </Card>
          ))}
        </div>


      </div>

      {/* Video Player Modal */}
      {playingVideo && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="relative w-full max-w-4xl aspect-video bg-black rounded-2xl overflow-hidden">
            {/* Close Button */}
            <button
              onClick={handleCloseVideo}
              className="absolute top-4 right-4 z-10 bg-black/60 backdrop-blur-sm rounded-full p-3 text-white hover:bg-black/80 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>

            {/* Video Title */}
            <div className="absolute top-4 left-4 z-10 bg-black/60 backdrop-blur-sm rounded-lg px-4 py-2">
              <h3 className="text-white font-semibold">{playingVideo.title}</h3>
            </div>

            {/* YouTube Embedded Player */}
            <iframe
              src={`https://www.youtube.com/embed/${playingVideo.videoId}?autoplay=1&rel=0&modestbranding=1`}
              className="w-full h-full"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              title={playingVideo.title}
            />
          </div>
        </div>
      )}
    </section>
  );
};

export default RecentProjects;