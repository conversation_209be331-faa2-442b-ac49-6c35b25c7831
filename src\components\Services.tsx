import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Instagram, Youtube, Smartphone, Camera, Plane, Zap } from "lucide-react";
import { useState } from "react";
import OrderFormPopup from "./OrderFormPopup";

const Services = () => {
  // Popup State Management
  // Reason for State: Control the visibility of the order form popup
  // Task Performed: Manages popup open/close state and selected service data
  // Linking Information: Internal - Used by Get Started buttons and OrderFormPopup component
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [selectedService, setSelectedService] = useState<any>(null);

  const services = [
    {
      icon: <Instagram className="h-12 w-12" />,
      title: "Instagram Reels",
      description: "Engaging vertical videos optimized for Instagram's algorithm with trending audio and effects.",
      features: ["Script writing", "Voice-over", "Trending audio sync", "Hashtag optimization"],
      price: "$9.99",
      duration: "15 seconds",
      color: "from-pink-500 to-purple-500",
      accent: "text-pink-400"
    },
    {
      icon: <Youtube className="h-12 w-12" />,
      title: "YouTube Shorts",
      description: "High-retention short videos designed to maximize watch time and subscriber growth.",
      features: ["Hook optimization", "Retention editing", "End screen CTAs", "SEO optimization"],
      price: "$9.99",
      duration: "15 seconds",
      color: "from-red-500 to-orange-500",
      accent: "text-red-400"
    },
    {
      icon: <Camera className="h-12 w-12" />,
      title: "Pinterest Reels",
      description: "Visually stunning vertical videos that drive traffic and inspire action on Pinterest.",
      features: ["Visual storytelling", "Pin optimization", "Text overlays", "Trend integration"],
      price: "$9.99",
      duration: "15 seconds",
      color: "from-orange-500 to-red-500",
      accent: "text-orange-400"
    },
    {
      icon: <Smartphone className="h-12 w-12" />,
      title: "TikTok Videos",
      description: "Viral-ready content that captures TikTok's unique style and trending formats.",
      features: ["Trend research", "Effect integration", "Sound optimization", "Viral tactics"],
      price: "$9.99",
      duration: "15 seconds",
      color: "from-purple-500 to-pink-500",
      accent: "text-purple-400"
    },
    {
      icon: <Plane className="h-12 w-12" />,
      title: "Drone Footage",
      description: "Professional aerial cinematography for real estate, events, and promotional content.",
      features: ["4K quality", "Cinematic angles", "Color grading", "Multiple formats"],
      price: "$9.99",
      duration: "15 seconds",
      color: "from-blue-500 to-cyan-500",
      accent: "text-blue-400"
    }
  ];

  // Get Started Handler
  // Reason for Function: Handle Get Started button clicks to open order form popup
  // Task Performed: Opens popup with selected service data for order placement
  // Linking Information: Internal - Used by Get Started buttons in service cards
  const handleGetStarted = (service: any) => {
    setSelectedService(service);
    setIsPopupOpen(true);
  };

  // Close Popup Handler
  // Reason for Function: Handle popup close events
  // Task Performed: Closes the order form popup and resets selected service
  // Linking Information: Internal - Used by OrderFormPopup component onClose prop
  const handleClosePopup = () => {
    setIsPopupOpen(false);
    setSelectedService(null);
  };

  return (
    <section id="services" className="py-20 bg-secondary relative overflow-hidden">
      {/* Premium Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-secondary via-background/50 to-secondary"></div>
      <div className="absolute top-0 right-1/3 w-96 h-96 bg-gradient-start/8 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-1/3 w-96 h-96 bg-gradient-end/8 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 lg:px-8 relative z-10">
        <div className="text-center mb-20">
          <Badge className="bg-hero-gradient text-white border-0 mb-6 px-6 py-3 text-sm font-semibold tracking-wide">
            🎯 Our Services
          </Badge>
          <h2 className="text-4xl md:text-6xl font-bold mb-8 leading-tight">
            Choose Your
            <span className="bg-hero-gradient bg-clip-text text-transparent block mt-2">
              Video Platform
            </span>
          </h2>
          <p className="text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            Specialized video creation services for every major platform.
            Each service includes professional script, voice-over, and unlimited revisions.
          </p>

          {/* Premium Divider */}
          <div className="flex items-center justify-center mt-8">
            <div className="h-px bg-gradient-to-r from-transparent via-accent to-transparent w-32"></div>
            <div className="mx-4 w-2 h-2 bg-accent rounded-full animate-pulse"></div>
            <div className="h-px bg-gradient-to-r from-transparent via-accent to-transparent w-32"></div>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {services.map((service, index) => (
            <Card
              key={index}
              className="bg-card-gradient border-service-border hover:border-accent/30 transition-all duration-500 hover:scale-105 group overflow-hidden relative"
            >
              {/* Premium Background Effects */}
              <div className={`absolute inset-0 bg-gradient-to-br ${service.color} opacity-5 group-hover:opacity-15 transition-opacity duration-500`}></div>
              <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-accent/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-gradient-start/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <CardHeader className="relative z-10 pb-4">
                {/* Premium Icon Container */}
                <div className="relative mb-6">
                  <div className="absolute inset-0 bg-gradient-to-br from-accent/20 to-transparent rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className={`${service.accent} mb-2 group-hover:scale-110 transition-transform duration-500 relative z-10`}>
                    {service.icon}
                  </div>
                  <div className="w-16 h-1 bg-gradient-to-r from-accent to-transparent rounded-full opacity-60"></div>
                </div>

                <CardTitle className="text-2xl font-bold text-foreground group-hover:text-accent transition-colors duration-300 mb-4">
                  {service.title}
                </CardTitle>
                <p className="text-muted-foreground leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">
                  {service.description}
                </p>
              </CardHeader>
              
              <CardContent className="relative space-y-8 z-10">
                {/* Premium Features List */}
                <div className="space-y-4">
                  <h4 className="text-sm font-semibold text-accent uppercase tracking-wide">What's Included</h4>
                  <div className="space-y-3">
                    {service.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center gap-3 group/feature">
                        <div className="w-3 h-3 bg-gradient-to-r from-accent to-gradient-start rounded-full flex-shrink-0 group-hover/feature:scale-110 transition-transform duration-300"></div>
                        <span className="text-sm text-muted-foreground group-hover/feature:text-foreground transition-colors duration-300">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Premium Pricing Section */}
                <div className="border-t border-gradient-to-r from-accent/20 via-border to-accent/20 pt-6">
                  <div className="flex items-center justify-between mb-6">
                    <div className="space-y-1">
                      <p className="text-3xl font-bold text-foreground group-hover:text-accent transition-colors duration-300">
                        {service.price}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        for {service.duration}
                      </p>
                    </div>
                    <Badge className={`${service.accent} bg-opacity-20 border-current backdrop-blur-sm shadow-lg`}>
                      Starting Price
                    </Badge>
                  </div>

                  {/* Premium Action Button */}
                  <Button
                    className="w-full bg-hero-gradient hover:opacity-90 transition-all duration-300 py-6 text-lg font-medium shadow-lg hover:shadow-xl hover:shadow-accent/25"
                    onClick={() => handleGetStarted(service)}
                  >
                    Get Started
                    <Zap className="ml-2 h-5 w-5" />
                  </Button>
                </div>

                {/* Premium Additional Info */}
                <div className="text-center pt-4 border-t border-gradient-to-r from-transparent via-border to-transparent">
                  <p className="text-xs text-muted-foreground group-hover:text-foreground/70 transition-colors duration-300">
                    Includes script, voice-over & unlimited revisions
                  </p>
                </div>

                {/* Premium Bottom Accent */}
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-accent/50 via-gradient-start/50 to-accent/50 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-center"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Premium Additional Services Section */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-accent/10 via-gradient-start/10 to-accent/10 rounded-3xl blur-xl"></div>
          <div className="relative bg-card-gradient border border-service-border rounded-3xl p-12 max-w-5xl mx-auto">
            <div className="text-center mb-8">
              <h3 className="text-3xl font-bold mb-4 text-foreground">
                What's Included in Every Service
              </h3>
              <div className="w-24 h-1 bg-gradient-to-r from-accent to-gradient-start rounded-full mx-auto"></div>
            </div>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="space-y-4 text-center group">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-accent/20 to-transparent rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="w-16 h-16 bg-hero-gradient rounded-full flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300 relative z-10">
                    <span className="text-2xl">📝</span>
                  </div>
                </div>
                <h4 className="font-semibold text-foreground group-hover:text-accent transition-colors duration-300">Professional Script</h4>
                <p className="text-sm text-muted-foreground group-hover:text-foreground/80 transition-colors duration-300">Engaging copy that converts</p>
              </div>

              <div className="space-y-4 text-center group">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-gradient-start/20 to-transparent rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="w-16 h-16 bg-hero-gradient rounded-full flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300 relative z-10">
                    <span className="text-2xl">🎙️</span>
                  </div>
                </div>
                <h4 className="font-semibold text-foreground group-hover:text-accent transition-colors duration-300">Voice-over</h4>
                <p className="text-sm text-muted-foreground group-hover:text-foreground/80 transition-colors duration-300">Any language available</p>
              </div>

              <div className="space-y-4 text-center group">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-accent/20 to-transparent rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="w-16 h-16 bg-hero-gradient rounded-full flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300 relative z-10">
                    <span className="text-2xl">🔄</span>
                  </div>
                </div>
                <h4 className="font-semibold text-foreground group-hover:text-accent transition-colors duration-300">Unlimited Revisions</h4>
                <p className="text-sm text-muted-foreground group-hover:text-foreground/80 transition-colors duration-300">Until you're happy</p>
              </div>

              <div className="space-y-4 text-center group">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-gradient-start/20 to-transparent rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="w-16 h-16 bg-hero-gradient rounded-full flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300 relative z-10">
                    <span className="text-2xl">⚡</span>
                  </div>
                </div>
                <h4 className="font-semibold text-foreground group-hover:text-accent transition-colors duration-300">Fast Delivery</h4>
                <p className="text-sm text-muted-foreground group-hover:text-foreground/80 transition-colors duration-300">Project-based timeline</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Order Form Popup */}
      <OrderFormPopup
        isOpen={isPopupOpen}
        onClose={handleClosePopup}
        service={selectedService}
      />
    </section>
  );
};

export default Services;