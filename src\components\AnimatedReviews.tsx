import { Badge } from "@/components/ui/badge";
import { <PERSON>, Quote } from "lucide-react";
import { useEffect, useState } from "react";

const AnimatedReviews = () => {
  // Review Images Data
  // Reason for Data: Store all review image filenames for dynamic loading
  // Task Performed: Defines array of review screenshots from WhatsApp
  // Linking Information: Internal - Used by carousel animation component
  const reviewImages = [
    "WhatsApp Image 2025-07-28 at 00.09.59_1e7410ca.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.00_a77a8df5.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.01_135ed059.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.01_627d006f.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.02_655d5683.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.02_7903adb2.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.02_8bbd3639.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.03_67fad1bb.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.03_b70119af.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.04_3a941785.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.04_76103d01.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.04_e33d2442.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.05_18538ce3.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.05_99fc06df.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.05_b759d49f.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.06_c5ea2fc2.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.06_f24e45a1.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.07_2969fad3.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.07_525afa9a.jpg",
    "WhatsApp Image 2025-07-28 at 00.10.07_e93f61ef.jpg"
  ];

  // Animation State Management
  // Reason for State: Control the continuous sliding animation
  // Task Performed: Manages current slide index and animation direction
  // Linking Information: Internal - Used by carousel animation effects
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(1); // 1 for forward, -1 for backward

  // Continuous Animation Effect
  // Reason for Effect: Create always-animating carousel with back-and-forth movement
  // Task Performed: Automatically advances slides and reverses direction at ends
  // Linking Information: Internal - Drives the main carousel animation
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => {
        const nextIndex = prevIndex + direction;
        
        // Reverse direction at the ends
        if (nextIndex >= reviewImages.length - 3) {
          setDirection(-1);
          return prevIndex - 1;
        } else if (nextIndex < 0) {
          setDirection(1);
          return prevIndex + 1;
        }
        
        return nextIndex;
      });
    }, 3000); // Change slide every 3 seconds

    return () => clearInterval(interval);
  }, [direction, reviewImages.length]);

  // Get Visible Reviews
  // Reason for Function: Calculate which review images should be visible
  // Task Performed: Returns array of 3 consecutive review images for display
  // Linking Information: Internal - Used by carousel display component
  const getVisibleReviews = () => {
    const visible = [];
    for (let i = 0; i < 3; i++) {
      const index = (currentIndex + i) % reviewImages.length;
      visible.push({
        image: reviewImages[index],
        index: index
      });
    }
    return visible;
  };

  return (
    <section id="reviews" className="py-20 bg-secondary relative overflow-hidden">
      {/* Premium Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-secondary via-background/50 to-secondary"></div>
      <div className="absolute top-1/4 left-1/3 w-96 h-96 bg-gradient-start/8 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-1/3 w-96 h-96 bg-gradient-end/8 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Badge className="bg-hero-gradient text-white border-0 mb-6 px-6 py-3 text-sm font-semibold tracking-wide">
            ⭐ Client Reviews
          </Badge>
          <h2 className="text-4xl md:text-6xl font-bold mb-8 leading-tight">
            What Our Clients
            <span className="bg-hero-gradient bg-clip-text text-transparent block mt-2">
              Say About Us
            </span>
          </h2>
          <p className="text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            Real feedback from real clients who've experienced the power of our 
            video creation services. See why they trust us with their brand.
          </p>
          
          {/* Premium Divider */}
          <div className="flex items-center justify-center mt-8">
            <div className="h-px bg-gradient-to-r from-transparent via-accent to-transparent w-32"></div>
            <div className="mx-4 w-2 h-2 bg-accent rounded-full animate-pulse"></div>
            <div className="h-px bg-gradient-to-r from-transparent via-accent to-transparent w-32"></div>
          </div>
        </div>

        {/* Animated Reviews Carousel */}
        <div className="relative max-w-7xl mx-auto">
          {/* Carousel Container */}
          <div className="overflow-hidden rounded-2xl">
            <div 
              className="flex transition-transform duration-1000 ease-in-out"
              style={{
                transform: `translateX(-${(currentIndex % reviewImages.length) * (100 / 3)}%)`
              }}
            >
              {/* Create extended array for seamless loop */}
              {[...reviewImages, ...reviewImages.slice(0, 3)].map((image, index) => (
                <div
                  key={`${image}-${index}`}
                  className="flex-shrink-0 w-1/3 px-4"
                >
                  <div className="relative group">
                    {/* Review Image Container */}
                    <div className="relative overflow-hidden rounded-xl shadow-2xl bg-card border border-service-border">
                      {/* Premium Background Effect */}
                      <div className="absolute inset-0 bg-gradient-to-br from-accent/5 via-transparent to-gradient-start/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                      
                      {/* Review Screenshot */}
                      <img
                        src={`/images/reviews/${image}`}
                        alt={`Client Review ${index + 1}`}
                        className="w-full h-auto object-cover transition-transform duration-500 group-hover:scale-105"
                        loading="lazy"
                      />
                      
                      {/* Overlay with Quote Icon */}
                      <div className="absolute top-4 right-4 bg-hero-gradient rounded-full p-3 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <Quote className="h-5 w-5 text-white" />
                      </div>
                      
                      {/* Premium Bottom Accent */}
                      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-accent/50 via-gradient-start/50 to-accent/50 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-center"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Direction Indicators */}
          <div className="flex justify-center mt-8 space-x-2">
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full transition-all duration-300 ${direction === 1 ? 'bg-accent' : 'bg-accent/30'}`}></div>
              <span className="text-sm text-muted-foreground">
                {direction === 1 ? 'Moving Forward' : 'Moving Backward'}
              </span>
              <div className={`w-3 h-3 rounded-full transition-all duration-300 ${direction === -1 ? 'bg-accent' : 'bg-accent/30'}`}></div>
            </div>
          </div>

          {/* Review Counter */}
          <div className="text-center mt-6">
            <p className="text-muted-foreground">
              Showing {Math.min(3, reviewImages.length)} of {reviewImages.length} reviews
            </p>
          </div>
        </div>

        {/* Stats Section */}
        <div className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div className="space-y-2">
            <div className="flex justify-center mb-2">
              {Array.from({ length: 5 }, (_, index) => (
                <Star key={index} className="h-5 w-5 text-yellow-400 fill-current" />
              ))}
            </div>
            <p className="text-3xl font-bold bg-hero-gradient bg-clip-text text-transparent">
              5.0
            </p>
            <p className="text-muted-foreground text-sm">Average Rating</p>
          </div>
          
          <div className="space-y-2">
            <p className="text-3xl font-bold bg-hero-gradient bg-clip-text text-transparent">
              500+
            </p>
            <p className="text-muted-foreground text-sm">Happy Clients</p>
          </div>
          
          <div className="space-y-2">
            <p className="text-3xl font-bold bg-hero-gradient bg-clip-text text-transparent">
              1000+
            </p>
            <p className="text-muted-foreground text-sm">Videos Created</p>
          </div>
          
          <div className="space-y-2">
            <p className="text-3xl font-bold bg-hero-gradient bg-clip-text text-transparent">
              98%
            </p>
            <p className="text-muted-foreground text-sm">Satisfaction Rate</p>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="relative max-w-3xl mx-auto">
            <div className="absolute inset-0 bg-gradient-to-r from-accent/10 via-gradient-start/10 to-accent/10 rounded-2xl blur-xl"></div>
            <div className="relative bg-card-gradient border border-service-border rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-foreground mb-4">
                Ready to Join Our Happy Clients?
              </h3>
              <p className="text-muted-foreground mb-6">
                Experience the same quality and results that our clients rave about.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <div className="flex items-center justify-center space-x-1">
                  {Array.from({ length: 5 }, (_, index) => (
                    <Star key={index} className="h-4 w-4 text-yellow-400 fill-current" />
                  ))}
                  <span className="ml-2 text-sm text-muted-foreground">
                    Trusted by 500+ clients
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AnimatedReviews;
