import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON><PERSON>, Zap, ArrowRight } from "lucide-react";

const CallToAction = () => {
  const handleWhatsAppClick = () => {
    const message = "Hi! I'm ready to start creating viral videos. Can you help me get started?";
    const whatsappUrl = `https://wa.me/your-number?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  const scrollToServices = () => {
    const element = document.getElementById('services');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="py-20 bg-secondary relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-hero-gradient opacity-5"></div>
      <div className="absolute top-20 left-20 w-40 h-40 bg-accent/5 rounded-full blur-xl"></div>
      <div className="absolute bottom-20 right-20 w-60 h-60 bg-gradient-start/5 rounded-full blur-xl"></div>
      
      <div className="container mx-auto px-4 lg:px-8 relative">
        <div className="max-w-4xl mx-auto text-center">
          <Badge className="bg-hero-gradient text-white border-0 mb-6">
            🚀 Ready to Go Viral?
          </Badge>
          
          <h2 className="text-4xl md:text-6xl font-bold mb-6">
            Let's Create Your
            <span className="bg-hero-gradient bg-clip-text text-transparent block">
              Next Viral Video
            </span>
          </h2>
          
          <p className="text-xl text-muted-foreground mb-12 max-w-2xl mx-auto">
            Join 1000+ creators and businesses who trust us to create engaging short videos 
            that drive real results. Starting at just $9.99 for 15 seconds.
          </p>

          {/* Key Benefits */}
          <div className="grid md:grid-cols-3 gap-6 mb-12">
            <div className="bg-card border border-service-border rounded-xl p-6 hover:border-accent/30 transition-colors">
              <div className="w-12 h-12 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-6 w-6 text-accent" />
              </div>
              <h3 className="font-semibold text-foreground mb-2">Fast Turnaround</h3>
              <p className="text-sm text-muted-foreground">Get your video delivered based on project requirements</p>
            </div>
            
            <div className="bg-card border border-service-border rounded-xl p-6 hover:border-accent/30 transition-colors">
              <div className="w-12 h-12 bg-gradient-start/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-xl">🔄</span>
              </div>
              <h3 className="font-semibold text-foreground mb-2">Unlimited Revisions</h3>
              <p className="text-sm text-muted-foreground">We work until you're 100% satisfied</p>
            </div>
            
            <div className="bg-card border border-service-border rounded-xl p-6 hover:border-accent/30 transition-colors">
              <div className="w-12 h-12 bg-gradient-end/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-xl">🌍</span>
              </div>
              <h3 className="font-semibold text-foreground mb-2">Any Language</h3>
              <p className="text-sm text-muted-foreground">Professional voice-over in any language</p>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-8">
            <Button
              size="lg"
              className="bg-hero-gradient hover:opacity-90 transition-opacity text-lg px-8 py-6 group"
              onClick={scrollToServices}
            >
              <MessageCircle className="mr-2 h-5 w-5" />
              Start Your Project Now
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
            
            <Button 
              size="lg" 
              variant="outline" 
              className="border-accent text-accent hover:bg-accent hover:text-white text-lg px-8 py-6"
              onClick={scrollToServices}
            >
              View All Services
            </Button>
          </div>

          {/* Urgency/Social Proof */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>Currently accepting new projects</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-accent font-semibold">500+</span>
              <span>creators already joined this month</span>
            </div>
          </div>

          {/* Pricing Reminder */}
          <div className="mt-12 p-6 bg-card border border-service-border rounded-2xl max-w-2xl mx-auto">
            <div className="flex items-center justify-between">
              <div className="text-left">
                <p className="text-sm text-muted-foreground mb-1">Starting from</p>
                <p className="text-3xl font-bold text-foreground">$9.99</p>
                <p className="text-sm text-muted-foreground">for 15 seconds + all inclusions</p>
              </div>
              <div className="text-right">
                <div className="text-xs text-muted-foreground space-y-1">
                  <div>✅ Professional Script</div>
                  <div>✅ Voice-over Included</div>
                  <div>✅ Unlimited Revisions</div>
                  <div>✅ Any Language</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CallToAction;