import { <PERSON>, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MessageCircle, Star, Award, Users, Zap } from "lucide-react";

const OurTeam = () => {
  // WhatsApp Contact Handler
  // Reason for Function: Handle WhatsApp contact button clicks for team inquiries
  // Task Performed: Opens WhatsApp chat in new tab with team-specific message
  // Linking Information: Internal - Used by contact buttons in team member cards
  const handleWhatsAppClick = () => {
    const whatsappNumber = '94777164818';
    const message = encodeURIComponent('Hi! I saw your team profile and would like to discuss a project with you.');
    window.open(`https://wa.me/${whatsappNumber}?text=${message}`, '_blank');
  };

  // Main Team Member Data
  // Reason for Data: Store main team member (speaker) information
  // Task Performed: Defines speaker's profile data for display
  // Linking Information: Internal - Used by main profile card component
  const mainTeamMember = {
    name: "<PERSON><PERSON>",
    role: "Video Editor & Graphic Designer",
    experience: "5+ Years",
    specialization: "Reels & Short-Form Content",
    image: "/images/IMG_9691.JPG",
    description: "Passionate video editor and graphic designer with over 5 years of specialized experience in creating viral Reels and short-form content. Expert in crafting engaging visual stories that capture attention and drive results.",
    skills: [
      "Instagram Reels",
      "YouTube Shorts",
      "TikTok Videos",
      "Graphic Design",
      "Motion Graphics",
      "Brand Identity"
    ],
    achievements: [
      "1000+ Videos Created",
      "500+ Happy Clients",
      "50+ Viral Campaigns",
      "5.0 Star Rating"
    ]
  };

  // Additional Team Members Data
  // Reason for Data: Store additional team members information (dummy data)
  // Task Performed: Defines supporting team members profiles for display
  // Linking Information: Internal - Used by team member cards component
  const additionalTeamMembers = [
    {
      id: 1,
      name: "Alex Rodriguez",
      role: "Video Editor",
      experience: "3+ Years",
      specialization: "Motion Graphics & Effects",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face",
      description: "Creative video editor specializing in motion graphics and visual effects. Expert in bringing stories to life through dynamic editing and seamless transitions.",
      skills: [
        "After Effects",
        "Premiere Pro",
        "Motion Graphics",
        "Color Grading",
        "Visual Effects",
        "Sound Design"
      ],
      projects: "300+ Projects Completed"
    },
    {
      id: 2,
      name: "Sarah Chen",
      role: "Graphic Designer",
      experience: "4+ Years", 
      specialization: "Brand Design & UI/UX",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face",
      description: "Talented graphic designer with expertise in brand identity and user interface design. Creates visually stunning designs that communicate brand messages effectively.",
      skills: [
        "Brand Identity",
        "UI/UX Design",
        "Illustration",
        "Typography",
        "Print Design",
        "Digital Marketing"
      ],
      projects: "250+ Designs Created"
    }
  ];

  return (
    <section id="our-team" className="py-20 bg-background relative overflow-hidden">
      {/* Premium Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-secondary/30 to-background"></div>
      <div className="absolute top-1/4 right-0 w-72 h-72 bg-gradient-start/8 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 left-0 w-72 h-72 bg-gradient-end/8 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto px-4 lg:px-8 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Badge className="bg-hero-gradient text-white border-0 mb-6 px-6 py-3 text-sm font-semibold tracking-wide">
            👥 Our Team
          </Badge>
          <h2 className="text-4xl md:text-6xl font-bold mb-8 leading-tight">
            Meet The Creative
            <span className="bg-hero-gradient bg-clip-text text-transparent block mt-2">
              Minds Behind
            </span>
            Your Success
          </h2>
          <p className="text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            Our talented team of video editors and graphic designers brings years of experience 
            and creative expertise to every project.
          </p>
          
          {/* Premium Divider */}
          <div className="flex items-center justify-center mt-8">
            <div className="h-px bg-gradient-to-r from-transparent via-accent to-transparent w-32"></div>
            <div className="mx-4 w-2 h-2 bg-accent rounded-full animate-pulse"></div>
            <div className="h-px bg-gradient-to-r from-transparent via-accent to-transparent w-32"></div>
          </div>
        </div>

        {/* Main Team Member (Speaker) */}
        <div className="mb-20">
          <div className="max-w-6xl mx-auto">
            <Card className="bg-card-gradient border-service-border hover:border-accent/30 transition-all duration-500 group overflow-hidden relative">
              {/* Premium Background Effects */}
              <div className="absolute inset-0 bg-gradient-to-br from-accent/5 via-transparent to-gradient-start/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-accent/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              
              <CardContent className="p-8 md:p-12 relative z-10">
                <div className="grid md:grid-cols-2 gap-12 items-center">
                  {/* Profile Image */}
                  <div className="relative">
                    <div className="relative w-80 h-80 mx-auto">
                      {/* Gradient Border */}
                      <div className="absolute inset-0 bg-hero-gradient rounded-full p-1">
                        <div className="w-full h-full bg-card rounded-full overflow-hidden">
                          <img
                            src={mainTeamMember.image}
                            alt={mainTeamMember.name}
                            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                          />
                        </div>
                      </div>
                      
                      {/* Floating Badge */}
                      <div className="absolute -top-4 -right-4 bg-hero-gradient text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                        Lead Designer
                      </div>
                      
                      {/* Experience Badge */}
                      <div className="absolute -bottom-4 -left-4 bg-card border border-accent rounded-lg p-3 shadow-lg">
                        <div className="flex items-center gap-2">
                          <Award className="h-5 w-5 text-accent" />
                          <span className="text-sm font-semibold">{mainTeamMember.experience}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Profile Content */}
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-3xl font-bold text-foreground mb-2 group-hover:text-accent transition-colors duration-300">
                        {mainTeamMember.name}
                      </h3>
                      <p className="text-xl text-accent font-semibold mb-2">
                        {mainTeamMember.role}
                      </p>
                      <p className="text-muted-foreground mb-4">
                        {mainTeamMember.specialization}
                      </p>
                      <p className="text-foreground/80 leading-relaxed">
                        {mainTeamMember.description}
                      </p>
                    </div>

                    {/* Skills */}
                    <div>
                      <h4 className="text-lg font-semibold text-foreground mb-3">Expertise</h4>
                      <div className="flex flex-wrap gap-2">
                        {mainTeamMember.skills.map((skill, index) => (
                          <Badge key={index} className="bg-accent/10 text-accent border-accent/20 hover:bg-accent/20 transition-colors">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Achievements */}
                    <div>
                      <h4 className="text-lg font-semibold text-foreground mb-3">Achievements</h4>
                      <div className="grid grid-cols-2 gap-4">
                        {mainTeamMember.achievements.map((achievement, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <Star className="h-4 w-4 text-accent" />
                            <span className="text-sm text-foreground/80">{achievement}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Contact Button */}
                    <div className="pt-4">
                      <Button
                        onClick={handleWhatsAppClick}
                        className="bg-hero-gradient hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl hover:shadow-accent/25"
                      >
                        <MessageCircle className="mr-2 h-5 w-5" />
                        Contact Our Team
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Additional Team Members */}
        <div>
          <h3 className="text-3xl font-bold text-center text-foreground mb-12">
            Our Creative
            <span className="bg-hero-gradient bg-clip-text text-transparent"> Team</span>
          </h3>
          
          <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            {additionalTeamMembers.map((member) => (
              <Card 
                key={member.id}
                className="bg-card-gradient border-service-border hover:border-accent/30 transition-all duration-500 hover:scale-105 group overflow-hidden relative"
              >
                {/* Premium Background Effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-accent/5 via-transparent to-gradient-start/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                
                <CardContent className="p-8 relative z-10">
                  <div className="text-center space-y-6">
                    {/* Profile Image */}
                    <div className="relative w-32 h-32 mx-auto">
                      <div className="absolute inset-0 bg-hero-gradient rounded-full p-1">
                        <div className="w-full h-full bg-card rounded-full overflow-hidden">
                          <img
                            src={member.image}
                            alt={member.name}
                            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Profile Info */}
                    <div>
                      <h4 className="text-xl font-bold text-foreground mb-2 group-hover:text-accent transition-colors duration-300">
                        {member.name}
                      </h4>
                      <p className="text-accent font-semibold mb-1">
                        {member.role}
                      </p>
                      <p className="text-sm text-muted-foreground mb-3">
                        {member.specialization}
                      </p>
                      <p className="text-sm text-foreground/80 leading-relaxed">
                        {member.description}
                      </p>
                    </div>

                    {/* Skills */}
                    <div>
                      <div className="flex flex-wrap gap-2 justify-center">
                        {member.skills.slice(0, 4).map((skill, index) => (
                          <Badge key={index} className="bg-accent/10 text-accent border-accent/20 text-xs">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Projects Count */}
                    <div className="pt-4 border-t border-service-border">
                      <div className="flex items-center justify-center gap-2">
                        <Users className="h-4 w-4 text-accent" />
                        <span className="text-sm font-semibold text-foreground">
                          {member.projects}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  {/* Premium Bottom Accent */}
                  <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-accent/50 via-gradient-start/50 to-accent/50 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-center"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="relative max-w-3xl mx-auto">
            <div className="absolute inset-0 bg-gradient-to-r from-accent/10 via-gradient-start/10 to-accent/10 rounded-2xl blur-xl"></div>
            <div className="relative bg-card-gradient border border-service-border rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-foreground mb-4">
                Ready to Work with Our Team?
              </h3>
              <p className="text-muted-foreground mb-6">
                Let's bring your creative vision to life with our expertise and passion.
              </p>
              <Button
                onClick={handleWhatsAppClick}
                className="bg-hero-gradient hover:opacity-90 transition-all duration-300 px-8 py-6 text-lg font-semibold shadow-lg hover:shadow-xl hover:shadow-accent/25"
              >
                <Zap className="mr-2 h-5 w-5" />
                Start Your Project Today
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default OurTeam;
