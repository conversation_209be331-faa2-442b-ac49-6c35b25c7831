import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Play, Star, Users, Zap, X } from "lucide-react";
import { useState } from "react";

const Hero = () => {
  // Video Configuration
  // Reason for Configuration: Centralized video settings for easy management
  // Task Performed: Stores video URL and display preferences
  // Linking Information: Internal - Used by video player component in Hero section
  const videoConfig = {
    // YouTube video: https://www.youtube.com/watch?v=sV7j_eCyz6k
    // Configured for autoplay, 720p default resolution, muted for autoplay compliance
    videoId: 'sV7j_eCyz6k',
    videoUrl: 'https://www.youtube.com/embed/sV7j_eCyz6k?autoplay=1&mute=1&controls=1&modestbranding=1&rel=0&vq=hd720',
    // YouTube thumbnail URLs (high quality)
    thumbnailUrl: 'https://img.youtube.com/vi/sV7j_eCyz6k/maxresdefault.jpg',
    hasVideo: true,
    autoplay: true,
    muted: true,
    controls: true
  };

  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  // Scroll to Services Handler
  // Reason for Function: Navigate to services section smoothly
  // Task Performed: Smooth scrolling to services section
  // Linking Information: Internal - Used by View Services button in Hero component
  const scrollToServices = () => {
    const element = document.getElementById('services');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // WhatsApp Contact Handler
  // Reason for Function: Handle WhatsApp contact button clicks
  // Task Performed: Opens WhatsApp chat in new tab with predefined number
  // Linking Information: Internal - Used by Start Your Project button in Hero component
  const handleWhatsAppClick = () => {
    // WhatsApp number: +94777164818 (without + sign for URL)
    const whatsappNumber = '94777164818';
    window.open(`https://wa.me/${whatsappNumber}`, '_blank');
  };

  // Video Play Handler
  // Reason for Function: Handle video play button clicks
  // Task Performed: Starts video playback or opens video in new tab if external
  // Linking Information: Internal - Used by play button overlay in Hero video section
  const handleVideoPlay = () => {
    if (videoConfig.hasVideo && videoConfig.videoUrl) {
      setIsVideoPlaying(true);
    } else {
      // Fallback: You can add a demo video URL here
      window.open('https://www.youtube.com/watch?v=dQw4w9WgXcQ', '_blank');
    }
  };

  return (
    <section id="hero" className="min-h-screen pt-16 flex items-center bg-gradient-to-br from-background via-background to-secondary">
      <div className="container mx-auto px-4 lg:px-8 py-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Text Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <Badge className="bg-hero-gradient text-white border-0 px-4 py-2">
                ✨ Premium Video Creation Services
              </Badge>
              <h1 className="text-4xl md:text-6xl font-bold leading-tight">
                Create Viral 
                <span className="bg-hero-gradient bg-clip-text text-transparent block">
                  Short Videos
                </span>
                That Get Results
              </h1>
              <p className="text-xl text-muted-foreground max-w-lg">
                Professional Instagram Reels, YouTube Shorts, TikTok videos, and drone footage 
                that capture attention and drive engagement. Starting at just $9.99 for 15 seconds.
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                size="lg"
                className="bg-hero-gradient hover:opacity-90 transition-opacity text-lg px-8 py-6"
                onClick={scrollToServices}
              >
                Start Your Project
                <Zap className="ml-2 h-5 w-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-accent text-accent hover:bg-accent hover:text-white text-lg px-8 py-6"
                onClick={scrollToServices}
              >
                View Services
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">Trusted by creators worldwide</p>
              <div className="flex flex-wrap gap-6 items-center">
                <div className="flex items-center gap-2">
                  <div className="bg-accent/10 p-2 rounded-full">
                    <Star className="h-5 w-5 text-accent" />
                  </div>
                  <div>
                    <p className="font-semibold text-accent">5.0 Rating</p>
                    <p className="text-xs text-muted-foreground">500+ Reviews</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="bg-gradient-start/10 p-2 rounded-full">
                    <Users className="h-5 w-5 text-gradient-start" />
                  </div>
                  <div>
                    <p className="font-semibold text-gradient-start">1000+</p>
                    <p className="text-xs text-muted-foreground">Happy Clients</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Service Highlights */}
            <div className="grid grid-cols-2 gap-4 pt-6 border-t border-border">
              <div className="space-y-1">
                <p className="font-semibold text-foreground">✅ Script Included</p>
                <p className="text-sm text-muted-foreground">Professional copywriting</p>
              </div>
              <div className="space-y-1">
                <p className="font-semibold text-foreground">🎙️ Voice-over</p>
                <p className="text-sm text-muted-foreground">Any language available</p>
              </div>
              <div className="space-y-1">
                <p className="font-semibold text-foreground">🔄 Unlimited Revisions</p>
                <p className="text-sm text-muted-foreground">Until you're satisfied</p>
              </div>
              <div className="space-y-1">
                <p className="font-semibold text-foreground">⚡ Fast Delivery</p>
                <p className="text-sm text-muted-foreground">Project-based timeline</p>
              </div>
            </div>
          </div>

          {/* Right Column - Video */}
          <div className="relative">
            <div className="relative w-80 h-80 md:w-96 md:h-96 lg:w-[500px] lg:h-[500px] mx-auto rounded-full overflow-hidden shadow-2xl group cursor-pointer">
              {/* Video Player or Thumbnail */}
              {videoConfig.hasVideo && videoConfig.videoUrl && isVideoPlaying ? (
                // Actual Video Player
                <div className="relative w-full h-full">
                  <iframe
                    src={videoConfig.videoUrl}
                    className="w-full h-full rounded-full"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                    title="Hero Video"
                  />
                  {/* Close Video Button */}
                  <button
                    onClick={() => setIsVideoPlaying(false)}
                    className="absolute top-4 right-4 bg-black/60 backdrop-blur-sm rounded-full p-2 text-white hover:bg-black/80 transition-colors"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ) : (
                // Thumbnail with Play Button
                <>
                  <img
                    src={videoConfig.thumbnailUrl}
                    alt="Video Creation Process - YouTube Thumbnail"
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                  <div
                    className="absolute inset-0 bg-black/30 flex items-center justify-center group-hover:bg-black/20 transition-colors"
                    onClick={handleVideoPlay}
                  >
                    <div className="bg-white/90 backdrop-blur-sm rounded-full p-6 group-hover:scale-110 transition-transform">
                      <Play className="h-8 w-8 text-primary ml-1" fill="currentColor" />
                    </div>
                  </div>
                  <div className="absolute bottom-6 left-6 right-6 md:bottom-8 md:left-8 md:right-8 lg:bottom-12 lg:left-12 lg:right-12">
                    <div className="bg-black/60 backdrop-blur-sm rounded-lg p-3 md:p-4">
                      <p className="text-white font-semibold text-center text-sm md:text-base">
                        {videoConfig.hasVideo ? 'Watch Our Process' : 'Demo Video Available'}
                      </p>
                      <p className="text-white/80 text-xs md:text-sm text-center">
                        {videoConfig.hasVideo ? 'See how we create viral content' : 'Click to view sample content'}
                      </p>
                    </div>
                  </div>
                </>
              )}
            </div>

            {/* Floating Elements - Responsive Positioning */}
            <div className="absolute -top-4 -right-4 md:-top-6 md:-right-6 lg:-top-8 lg:-right-8 bg-card border border-border rounded-lg p-3 md:p-4 shadow-lg animate-float">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-xs md:text-sm font-medium">Live Production</span>
              </div>
            </div>

            <div className="absolute -bottom-4 -left-4 md:-bottom-6 md:-left-6 lg:-bottom-8 lg:-left-8 bg-card border border-border rounded-lg p-3 md:p-4 shadow-lg animate-float" style={{animationDelay: '2s'}}>
              <div className="text-center">
                <p className="text-xl md:text-2xl font-bold text-accent">$9.99</p>
                <p className="text-xs text-muted-foreground">Starting Price</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;