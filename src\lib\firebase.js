// Firebase Configuration and Initialization
// Reason for Configuration: Set up Firebase services for file storage and database
// Task Performed: Initializes Firebase app, Storage, and Firestore services
// Linking Information: Internal - Used throughout the app for file uploads and data storage

import { initializeApp } from 'firebase/app';
import { getStorage } from 'firebase/storage';
import { getFirestore } from 'firebase/firestore';

// Firebase configuration object using environment variables
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID
};

// Validate configuration
if (!firebaseConfig.apiKey || !firebaseConfig.projectId) {
  console.error('❌ Firebase configuration missing! Please check your .env.local file');
  console.log('📝 Required environment variables:');
  console.log('   VITE_FIREBASE_API_KEY');
  console.log('   VITE_FIREBASE_AUTH_DOMAIN');
  console.log('   VITE_FIREBASE_PROJECT_ID');
  console.log('   VITE_FIREBASE_STORAGE_BUCKET');
  console.log('   VITE_FIREBASE_MESSAGING_SENDER_ID');
  console.log('   VITE_FIREBASE_APP_ID');
}

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const storage = getStorage(app);
export const db = getFirestore(app);

// Export the app instance
export default app;
