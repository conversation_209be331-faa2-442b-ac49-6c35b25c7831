import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { X, Upload, FileText, Image, Send } from "lucide-react";

// Service Interface
// Reason for Interface: Define the structure of service data passed to the popup
// Task Performed: Type safety for service information
// Linking Information: Internal - Used by OrderFormPopup component for service details
interface Service {
  title: string;
  price: string;
  duration: string;
  description: string;
  features: string[];
}

// Duration Option Interface
// Reason for Interface: Define the structure of duration options with pricing
// Task Performed: Type safety for duration selection and pricing calculations
// Linking Information: Internal - Used by OrderFormPopup component for duration handling
interface DurationOption {
  seconds: number;
  label: string;
  price: number;
  originalPrice?: number;
  savings?: string;
  badge?: string;
}

// Form Data Interface
// Reason for Interface: Define the structure of form data collected from user
// Task Performed: Type safety for form validation and WhatsApp message generation
// Linking Information: Internal - Used by OrderFormPopup component for form handling
interface FormData {
  name: string;
  email: string;
  requirements: string;
  attachments: File[];
  selectedDuration: number;
}

// Props Interface
// Reason for Interface: Define the props structure for the OrderFormPopup component
// Task Performed: Type safety for component props
// Linking Information: Internal - Used by parent components to pass data to OrderFormPopup
interface OrderFormPopupProps {
  isOpen: boolean;
  onClose: () => void;
  service: Service | null;
}

const OrderFormPopup = ({ isOpen, onClose, service }: OrderFormPopupProps) => {
  // Strategic Pricing Configuration
  // Reason for Configuration: Psychological pricing to make 60s package most attractive
  // Task Performed: Defines pricing structure that encourages 60s selection
  // Linking Information: Internal - Used by duration selection and price calculation
  const durationOptions: DurationOption[] = [
    {
      seconds: 15,
      label: '15 seconds',
      price: 9.99,
      badge: 'Basic'
    },
    {
      seconds: 30,
      label: '30 seconds',
      price: 19.99,
      originalPrice: 19.99,
      badge: 'Standard'
    },
    {
      seconds: 45,
      label: '45 seconds',
      price: 22.99,
      originalPrice: 29.97,
      savings: 'Save $6.98',
      badge: 'Popular'
    },
    {
      seconds: 60,
      label: '60 seconds',
      price: 24.99,
      originalPrice: 39.96,
      savings: 'Save $14.97',
      badge: 'BEST VALUE'
    }
  ];

  // Form State Management
  // Reason for State: Track user input data for form submission
  // Task Performed: Manages form data and validation states
  // Linking Information: Internal - Used throughout the component for form handling
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    requirements: '',
    attachments: [],
    selectedDuration: 60 // Default to 60s (best value)
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Partial<FormData>>({});

  // Price Calculation Function
  // Reason for Function: Calculate price based on selected duration
  // Task Performed: Returns pricing details for selected duration
  // Linking Information: Internal - Used by UI components to display current pricing
  const getSelectedDurationDetails = (): DurationOption => {
    return durationOptions.find(option => option.seconds === formData.selectedDuration) || durationOptions[3];
  };

  // Duration Change Handler
  // Reason for Function: Handle duration selection changes
  // Task Performed: Updates selected duration in form state
  // Linking Information: Internal - Used by duration selection radio buttons
  const handleDurationChange = (duration: number) => {
    setFormData(prev => ({ ...prev, selectedDuration: duration }));
  };

  // File Upload Handler
  // Reason for Function: Handle file upload functionality for attachments
  // Task Performed: Processes file selection and updates form state
  // Linking Information: Internal - Used by file input onChange event
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const newFiles = Array.from(files);
      setFormData(prev => ({
        ...prev,
        attachments: [...prev.attachments, ...newFiles]
      }));
    }
  };

  // Remove File Handler
  // Reason for Function: Allow users to remove uploaded files
  // Task Performed: Removes specific file from attachments array
  // Linking Information: Internal - Used by file removal buttons
  const removeFile = (index: number) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }));
  };

  // Form Validation
  // Reason for Function: Validate form data before submission
  // Task Performed: Checks required fields and email format
  // Linking Information: Internal - Used by form submission handler
  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.requirements.trim()) {
      newErrors.requirements = 'Please describe your requirements';
    }

    if (!formData.selectedDuration || !durationOptions.some(option => option.seconds === formData.selectedDuration)) {
      newErrors.selectedDuration = 'Please select a duration';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // WhatsApp Message Generator
  // Reason for Function: Format form data and service details into WhatsApp message
  // Task Performed: Creates structured message with all order details including duration and pricing
  // Linking Information: Internal - Used by form submission to generate WhatsApp URL
  const generateWhatsAppMessage = (): string => {
    if (!service) return '';

    const selectedDuration = getSelectedDurationDetails();
    const savingsText = selectedDuration.savings ? ` (${selectedDuration.savings})` : '';

    const message = `🎬 *NEW ORDER REQUEST*

📋 *Service Selected:* ${service.title}
⏱️ *Duration:* ${selectedDuration.label}
💰 *Price:* $${selectedDuration.price}${savingsText}
🏷️ *Package:* ${selectedDuration.badge}

👤 *Customer Details:*
• Name: ${formData.name}
• Email: ${formData.email}

📝 *Requirements:*
${formData.requirements}

✨ *Service Features:*
${service.features.map(feature => `• ${feature}`).join('\n')}

📎 *Attachments:* ${formData.attachments.length > 0 ? `${formData.attachments.length} file(s) uploaded` : 'No attachments'}

---
*This order was placed through the website.*`;

    return encodeURIComponent(message);
  };

  // Form Submission Handler
  // Reason for Function: Handle form submission and redirect to WhatsApp
  // Task Performed: Validates form, generates message, and opens WhatsApp
  // Linking Information: Internal - Used by Place Order button onClick event
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Generate WhatsApp message
      const message = generateWhatsAppMessage();
      const whatsappNumber = '94777164818';
      const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${message}`;

      // Open WhatsApp in new tab
      window.open(whatsappUrl, '_blank');

      // Close popup after successful submission
      setTimeout(() => {
        onClose();
        setFormData({
          name: '',
          email: '',
          requirements: '',
          attachments: [],
          selectedDuration: 60
        });
        setErrors({});
      }, 1000);

    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Input Change Handler
  // Reason for Function: Handle form input changes
  // Task Performed: Updates form state and clears related errors
  // Linking Information: Internal - Used by form inputs onChange events
  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  if (!isOpen || !service) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-card border border-service-border rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Fixed Header */}
        <div className="bg-card border-b border-service-border p-6 rounded-t-2xl flex-shrink-0 z-10">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-foreground">Place Your Order</h2>
              <Badge className="bg-hero-gradient text-white border-0 mt-2">
                {service.title}
              </Badge>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-muted-foreground hover:text-foreground"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </div>

        {/* Scrollable Form Content */}
        <div className="flex-1 overflow-y-auto">

        {/* Form Content */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6 pb-8">
          {/* Service Summary */}
          <div className="bg-secondary/50 rounded-lg p-4 border border-service-border">
            <h3 className="font-semibold text-foreground mb-4">Service: {service.title}</h3>
            <div className="bg-card rounded-lg p-4 border border-accent/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold text-accent">
                    ${getSelectedDurationDetails().price}
                  </p>
                  {getSelectedDurationDetails().originalPrice && (
                    <p className="text-sm text-muted-foreground line-through">
                      ${getSelectedDurationDetails().originalPrice}
                    </p>
                  )}
                </div>
                <div className="text-right">
                  <Badge className="bg-hero-gradient text-white border-0 mb-1">
                    {getSelectedDurationDetails().badge}
                  </Badge>
                  {getSelectedDurationDetails().savings && (
                    <p className="text-sm text-green-400 font-semibold">
                      {getSelectedDurationDetails().savings}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Duration Selection */}
          <div className="space-y-4 pt-2">
            <Label className="text-foreground font-medium text-lg">
              Choose Video Duration *
            </Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pb-4">
              {durationOptions.map((option) => (
                <div
                  key={option.seconds}
                  className={`relative border-2 rounded-lg p-4 cursor-pointer transition-all duration-300 ${
                    formData.selectedDuration === option.seconds
                      ? 'border-accent bg-accent/10'
                      : 'border-service-border hover:border-accent/50'
                  }`}
                  onClick={() => handleDurationChange(option.seconds)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <input
                        type="radio"
                        name="duration"
                        value={option.seconds}
                        checked={formData.selectedDuration === option.seconds}
                        onChange={() => handleDurationChange(option.seconds)}
                        className="w-4 h-4 text-accent"
                      />
                      <div>
                        <p className="font-semibold text-foreground">{option.label}</p>
                        <p className="text-sm text-muted-foreground">{option.badge}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-accent">${option.price}</p>
                      {option.originalPrice && option.originalPrice !== option.price && (
                        <p className="text-sm text-muted-foreground line-through">
                          ${option.originalPrice}
                        </p>
                      )}
                      {option.savings && (
                        <p className="text-xs text-green-400 font-semibold">
                          {option.savings}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Best Value Badge */}
                  {option.badge === 'BEST VALUE' && (
                    <div className="absolute -top-2 -right-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                      BEST VALUE
                    </div>
                  )}

                  {/* Popular Badge */}
                  {option.badge === 'Popular' && (
                    <div className="absolute -top-2 -right-2 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                      POPULAR
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Name Field */}
          <div className="space-y-2">
            <Label htmlFor="name" className="text-foreground font-medium">
              Full Name *
            </Label>
            <Input
              id="name"
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Enter your full name"
              className={`bg-background border-service-border ${errors.name ? 'border-red-500' : ''}`}
            />
            {errors.name && (
              <p className="text-red-500 text-sm">{errors.name}</p>
            )}
          </div>

          {/* Email Field */}
          <div className="space-y-2">
            <Label htmlFor="email" className="text-foreground font-medium">
              Email Address *
            </Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="Enter your email address"
              className={`bg-background border-service-border ${errors.email ? 'border-red-500' : ''}`}
            />
            {errors.email && (
              <p className="text-red-500 text-sm">{errors.email}</p>
            )}
          </div>

          {/* Requirements Field */}
          <div className="space-y-2">
            <Label htmlFor="requirements" className="text-foreground font-medium">
              Project Requirements *
            </Label>
            <Textarea
              id="requirements"
              value={formData.requirements}
              onChange={(e) => handleInputChange('requirements', e.target.value)}
              placeholder="Describe your project requirements, style preferences, target audience, etc."
              rows={4}
              className={`bg-background border-service-border resize-none ${errors.requirements ? 'border-red-500' : ''}`}
            />
            {errors.requirements && (
              <p className="text-red-500 text-sm">{errors.requirements}</p>
            )}
          </div>

          {/* File Upload */}
          <div className="space-y-2">
            <Label className="text-foreground font-medium">
              Attachments (Optional)
            </Label>
            <div className="border-2 border-dashed border-service-border rounded-lg p-4 text-center">
              <input
                type="file"
                multiple
                onChange={handleFileUpload}
                className="hidden"
                id="file-upload"
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
              />
              <label
                htmlFor="file-upload"
                className="cursor-pointer flex flex-col items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
              >
                <Upload className="h-8 w-8" />
                <span>Click to upload files</span>
                <span className="text-xs">PDF, DOC, Images (Max 10MB each)</span>
              </label>
            </div>

            {/* Uploaded Files */}
            {formData.attachments.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm font-medium text-foreground">Uploaded Files:</p>
                {formData.attachments.map((file, index) => (
                  <div key={index} className="flex items-center justify-between bg-secondary/50 rounded-lg p-3">
                    <div className="flex items-center gap-2">
                      {file.type.startsWith('image/') ? (
                        <Image className="h-4 w-4 text-blue-500" />
                      ) : (
                        <FileText className="h-4 w-4 text-green-500" />
                      )}
                      <span className="text-sm text-foreground">{file.name}</span>
                      <span className="text-xs text-muted-foreground">
                        ({(file.size / 1024 / 1024).toFixed(2)} MB)
                      </span>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Submit Button */}
          <div className="pt-4 border-t border-service-border">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-hero-gradient hover:opacity-90 transition-opacity py-6 text-lg font-semibold"
            >
              {isSubmitting ? (
                'Processing...'
              ) : (
                <>
                  <Send className="mr-2 h-5 w-5" />
                  Place Order via WhatsApp
                </>
              )}
            </Button>
            <p className="text-xs text-muted-foreground text-center mt-2">
              You'll be redirected to WhatsApp with your order details
            </p>
          </div>
        </form>
        </div>
      </div>
    </div>
  );
};

export default OrderFormPopup;
