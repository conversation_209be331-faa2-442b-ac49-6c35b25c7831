// File Upload Service for Firebase Storage
// Reason for Service: Handle file uploads to Firebase Storage and metadata storage
// Task Performed: Uploads files, generates download URLs, stores metadata in Firestore
// Linking Information: Internal - Used by OrderFormPopup component for attachment handling

import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { storage, db } from './firebase';

// Upload File to Firebase Storage
// Reason for Function: Upload individual file to Firebase Storage
// Task Performed: Creates storage reference, uploads file, returns download URL
// Linking Information: Internal - Used by uploadAttachments function
export const uploadFileToStorage = async (file, clientName) => {
  try {
    // Create unique filename with timestamp
    const timestamp = Date.now();
    const fileName = `${timestamp}_${file.name}`;
    
    // Create storage reference
    const storageRef = ref(storage, `attachments/${clientName}/${fileName}`);
    
    // Upload file
    const snapshot = await uploadBytes(storageRef, file);
    
    // Get download URL
    const downloadURL = await getDownloadURL(snapshot.ref);
    
    return {
      success: true,
      downloadURL,
      fileName,
      originalName: file.name,
      size: file.size,
      type: file.type
    };
  } catch (error) {
    console.error('Error uploading file:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Store File Metadata in Firestore
// Reason for Function: Store file metadata and client information in database
// Task Performed: Creates document in Firestore with file details and auto-increment ID
// Linking Information: Internal - Used by uploadAttachments function
export const storeFileMetadata = async (fileData, clientData) => {
  try {
    const docRef = await addDoc(collection(db, 'attachments'), {
      // Client information
      clientName: clientData.name,
      clientEmail: clientData.email,
      clientPhone: clientData.phone,
      
      // File information
      fileName: fileData.originalName,
      storedFileName: fileData.fileName,
      downloadURL: fileData.downloadURL,
      fileSize: fileData.size,
      fileType: fileData.type,
      
      // Service information
      serviceTitle: clientData.serviceTitle,
      videoDuration: clientData.videoDuration,
      videoCount: clientData.videoCount,
      totalPrice: clientData.totalPrice,
      
      // Metadata
      uploadDate: serverTimestamp(),
      status: 'uploaded'
    });
    
    return {
      success: true,
      documentId: docRef.id,
      ...fileData
    };
  } catch (error) {
    console.error('Error storing metadata:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Upload Multiple Attachments
// Reason for Function: Handle multiple file uploads and metadata storage
// Task Performed: Uploads all files, stores metadata, returns array of results
// Linking Information: Internal - Used by OrderFormPopup component
export const uploadAttachments = async (files, clientData) => {
  const uploadResults = [];
  
  try {
    for (const file of files) {
      // Upload file to storage
      const uploadResult = await uploadFileToStorage(file, clientData.name);
      
      if (uploadResult.success) {
        // Store metadata in Firestore
        const metadataResult = await storeFileMetadata(uploadResult, clientData);
        
        if (metadataResult.success) {
          uploadResults.push({
            id: metadataResult.documentId,
            name: uploadResult.originalName,
            url: uploadResult.downloadURL,
            size: uploadResult.size,
            type: uploadResult.type,
            success: true
          });
        } else {
          uploadResults.push({
            name: file.name,
            error: metadataResult.error,
            success: false
          });
        }
      } else {
        uploadResults.push({
          name: file.name,
          error: uploadResult.error,
          success: false
        });
      }
    }
    
    return uploadResults;
  } catch (error) {
    console.error('Error uploading attachments:', error);
    throw new Error('Failed to upload attachments: ' + error.message);
  }
};

// Generate WhatsApp Attachment Links
// Reason for Function: Format uploaded file links for WhatsApp message
// Task Performed: Creates formatted string with file names and download links
// Linking Information: Internal - Used by WhatsApp message generation
export const generateAttachmentLinks = (uploadResults) => {
  const successfulUploads = uploadResults.filter(result => result.success);
  
  if (successfulUploads.length === 0) {
    return 'No attachments uploaded';
  }
  
  return successfulUploads.map((file, index) => 
    `📎 ${index + 1}. ${file.name}\n   🔗 ${file.url}`
  ).join('\n\n');
};
