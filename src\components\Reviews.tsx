import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, Quote } from "lucide-react";

const Reviews = () => {
  const reviews = [
    {
      id: 1,
      name: "<PERSON>",
      role: "Fashion Influencer",
      avatar: "/api/placeholder/64/64",
      rating: 5,
      text: "Absolutely amazing work! My Instagram Reels got 10x more engagement after working with them. The quality is outstanding and they really understand what works on social media.",
      platform: "Instagram Reels",
      results: "10x engagement increase"
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "Tech Startup Founder",
      avatar: "/api/placeholder/64/64",
      rating: 5,
      text: "Their YouTube Shorts helped us reach 100K subscribers in just 3 months. The storytelling and editing quality is professional-grade. Highly recommended!",
      platform: "YouTube Shorts",
      results: "100K subscribers in 3 months"
    },
    {
      id: 3,
      name: "<PERSON>",
      role: "Real Estate Agent",
      avatar: "/api/placeholder/64/64",
      rating: 5,
      text: "The drone footage for our property listings is breathtaking. We've seen a 40% increase in inquiries since we started using their services. Worth every penny!",
      platform: "Drone Footage",
      results: "40% more inquiries"
    },
    {
      id: 4,
      name: "<PERSON>",
      role: "Restaurant Owner",
      avatar: "/api/placeholder/64/64",
      rating: 5,
      text: "Our TikTok videos went viral thanks to their creative approach. They know exactly what content performs well and how to make it engaging for our audience.",
      platform: "TikTok Videos",
      results: "Viral content success"
    },
    {
      id: 5,
      name: "Lisa Wang",
      role: "E-commerce Brand",
      avatar: "/api/placeholder/64/64",
      rating: 5,
      text: "Pinterest Reels created by them drove massive traffic to our website. The conversion rate from these videos is incredible. They understand each platform perfectly.",
      platform: "Pinterest Reels",
      results: "Massive traffic increase"
    },
    {
      id: 6,
      name: "James Miller",
      role: "Fitness Coach",
      avatar: "/api/placeholder/64/64",
      rating: 5,
      text: "Professional quality, unlimited revisions, and they delivered exactly what I envisioned. My client base has grown significantly since we started working together.",
      platform: "Multi-platform",
      results: "Significant client growth"
    }
  ];

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <section id="reviews" className="py-20 bg-background">
      <div className="container mx-auto px-4 lg:px-8">
        <div className="text-center mb-16">
          <Badge className="bg-hero-gradient text-white border-0 mb-4">
            Customer Reviews
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            What Our Clients
            <span className="bg-hero-gradient bg-clip-text text-transparent block">
              Are Saying
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Don't just take our word for it. See how we've helped creators and businesses 
            achieve incredible results with our video creation services.
          </p>
        </div>

        {/* Overall Rating Stats */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-4 bg-card border border-service-border rounded-2xl p-6">
            <div className="text-center">
              <div className="text-4xl font-bold text-accent mb-2">5.0</div>
              <div className="flex gap-1 mb-2">
                {renderStars(5)}
              </div>
              <div className="text-sm text-muted-foreground">Overall Rating</div>
            </div>
            <div className="w-px h-16 bg-border"></div>
            <div className="text-center">
              <div className="text-4xl font-bold text-gradient-start mb-2">500+</div>
              <div className="text-sm text-muted-foreground">Happy Clients</div>
            </div>
            <div className="w-px h-16 bg-border"></div>
            <div className="text-center">
              <div className="text-4xl font-bold text-gradient-end mb-2">1000+</div>
              <div className="text-sm text-muted-foreground">Videos Created</div>
            </div>
          </div>
        </div>

        {/* Reviews Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {reviews.map((review) => (
            <Card 
              key={review.id} 
              className="bg-card-gradient border-service-border hover:border-accent/30 transition-all duration-300 hover:scale-105 group relative overflow-hidden"
            >
              <CardContent className="p-6">
                {/* Quote Icon */}
                <div className="absolute top-4 right-4 text-accent/20 group-hover:text-accent/40 transition-colors">
                  <Quote className="h-8 w-8" />
                </div>

                {/* Rating */}
                <div className="flex gap-1 mb-4">
                  {renderStars(review.rating)}
                </div>

                {/* Review Text */}
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  "{review.text}"
                </p>

                {/* Results Badge */}
                <Badge className="bg-accent/10 text-accent border-accent/20 mb-4">
                  {review.results}
                </Badge>

                {/* Reviewer Info */}
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarImage src={review.avatar} alt={review.name} />
                    <AvatarFallback>{review.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-semibold text-foreground">{review.name}</p>
                    <p className="text-sm text-muted-foreground">{review.role}</p>
                    <p className="text-xs text-accent">{review.platform}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Trust Indicators */}
        <div className="mt-16 text-center">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            <div className="space-y-2">
              <div className="text-3xl font-bold text-accent">98%</div>
              <div className="text-sm text-muted-foreground">Client Satisfaction</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl font-bold text-gradient-start">24h</div>
              <div className="text-sm text-muted-foreground">Average Response</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl font-bold text-gradient-end">100%</div>
              <div className="text-sm text-muted-foreground">On-Time Delivery</div>
            </div>
            <div className="space-y-2">
              <div className="text-3xl font-bold text-purple-400">∞</div>
              <div className="text-sm text-muted-foreground">Free Revisions</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Reviews;