import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Zap, Globe, Infinity, Clock, Award, Headphones } from "lucide-react";

const WhyChooseUs = () => {
  const features = [
    {
      icon: <Zap className="h-8 w-8" />,
      title: "Lightning Fast Delivery",
      description: "Quick turnaround times without compromising on quality. Get your videos when you need them.",
      color: "text-yellow-400"
    },
    {
      icon: <Globe className="h-8 w-8" />,
      title: "Any Language Support",
      description: "We create content in any language with native voice-over artists for global reach.",
      color: "text-blue-400"
    },
    {
      icon: <Infinity className="h-8 w-8" />,
      title: "Unlimited Revisions",
      description: "We work until you're 100% satisfied. Your vision, perfectly executed.",
      color: "text-purple-400"
    },
    {
      icon: <Award className="h-8 w-8" />,
      title: "Premium Quality",
      description: "Professional scripts, voice-overs, and editing that make your content stand out.",
      color: "text-green-400"
    },
    {
      icon: <Clock className="h-8 w-8" />,
      title: "Flexible Timeline",
      description: "Project-based delivery that adapts to your schedule and requirements.",
      color: "text-orange-400"
    },
    {
      icon: <Headphones className="h-8 w-8" />,
      title: "24/7 Support",
      description: "Always here to help with any questions or adjustments you need.",
      color: "text-pink-400"
    }
  ];

  return (
    <section id="why-choose-us" className="py-20 bg-secondary relative overflow-hidden">
      {/* Premium Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-secondary via-secondary to-background opacity-50"></div>
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-start/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-end/5 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 lg:px-8 relative z-10">
        <div className="text-center mb-20">
          <Badge className="bg-hero-gradient text-white border-0 mb-6 px-6 py-3 text-sm font-semibold tracking-wide">
            ✨ Why Choose Us
          </Badge>
          <h2 className="text-4xl md:text-6xl font-bold mb-8 leading-tight">
            What Makes Us
            <span className="bg-hero-gradient bg-clip-text text-transparent block mt-2">
              Different
            </span>
          </h2>
          <p className="text-xl md:text-2xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
            We don't just create videos – we craft experiences that connect with your audience
            and drive real results for your brand.
          </p>

          {/* Premium Divider */}
          <div className="flex items-center justify-center mt-8">
            <div className="h-px bg-gradient-to-r from-transparent via-accent to-transparent w-32"></div>
            <div className="mx-4 w-2 h-2 bg-accent rounded-full"></div>
            <div className="h-px bg-gradient-to-r from-transparent via-accent to-transparent w-32"></div>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => (
            <Card
              key={index}
              className="bg-card-gradient border-service-border hover:border-accent/30 transition-all duration-500 hover:scale-105 group relative overflow-hidden"
            >
              {/* Premium Card Background Effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-accent/5 via-transparent to-gradient-start/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-accent/10 to-transparent rounded-bl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              <CardContent className="p-8 relative z-10">
                {/* Premium Icon Container */}
                <div className="relative mb-8">
                  <div className="absolute inset-0 bg-gradient-to-br from-accent/20 to-transparent rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className={`${feature.color} mb-2 group-hover:scale-110 transition-transform duration-500 relative z-10`}>
                    {feature.icon}
                  </div>
                  <div className="w-12 h-1 bg-gradient-to-r from-accent to-transparent rounded-full opacity-60"></div>
                </div>

                <h3 className="text-xl font-bold mb-4 text-foreground group-hover:text-accent transition-colors duration-300">
                  {feature.title}
                </h3>
                <p className="text-muted-foreground leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">
                  {feature.description}
                </p>

                {/* Premium Bottom Accent */}
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-accent/50 via-gradient-start/50 to-accent/50 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Premium Stats Section */}
        <div className="relative">
          {/* Stats Background */}
          <div className="absolute inset-0 bg-gradient-to-r from-accent/5 via-gradient-start/5 to-accent/5 rounded-3xl blur-xl"></div>

          <div className="relative bg-card-gradient border border-service-border rounded-3xl p-8 md:p-12">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div className="space-y-3 group">
                <div className="relative">
                  <p className="text-4xl md:text-5xl font-bold bg-hero-gradient bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300">
                    1000+
                  </p>
                  <div className="absolute -inset-2 bg-gradient-to-r from-accent/20 to-gradient-start/20 rounded-lg blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <p className="text-muted-foreground font-medium">Videos Created</p>
                <div className="w-8 h-1 bg-gradient-to-r from-accent to-gradient-start rounded-full mx-auto opacity-60"></div>
              </div>

              <div className="space-y-3 group">
                <div className="relative">
                  <p className="text-4xl md:text-5xl font-bold bg-hero-gradient bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300">
                    500+
                  </p>
                  <div className="absolute -inset-2 bg-gradient-to-r from-gradient-start/20 to-accent/20 rounded-lg blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <p className="text-muted-foreground font-medium">Happy Clients</p>
                <div className="w-8 h-1 bg-gradient-to-r from-gradient-start to-accent rounded-full mx-auto opacity-60"></div>
              </div>

              <div className="space-y-3 group">
                <div className="relative">
                  <p className="text-4xl md:text-5xl font-bold bg-hero-gradient bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300">
                    50+
                  </p>
                  <div className="absolute -inset-2 bg-gradient-to-r from-accent/20 to-gradient-start/20 rounded-lg blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <p className="text-muted-foreground font-medium">Languages</p>
                <div className="w-8 h-1 bg-gradient-to-r from-accent to-gradient-start rounded-full mx-auto opacity-60"></div>
              </div>

              <div className="space-y-3 group">
                <div className="relative">
                  <p className="text-4xl md:text-5xl font-bold bg-hero-gradient bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300">
                    5.0
                  </p>
                  <div className="absolute -inset-2 bg-gradient-to-r from-gradient-start/20 to-accent/20 rounded-lg blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <p className="text-muted-foreground font-medium">Average Rating</p>
                <div className="w-8 h-1 bg-gradient-to-r from-gradient-start to-accent rounded-full mx-auto opacity-60"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;