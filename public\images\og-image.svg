<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Gradient -->
  <rect width="1200" height="630" fill="url(#bgGradient)"/>
  
  <!-- Background Pattern -->
  <defs>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    </pattern>
  </defs>
  <rect width="1200" height="630" fill="url(#grid)"/>
  
  <!-- Main Content Container -->
  <g transform="translate(100, 150)">
    
    <!-- Logo/Icon -->
    <circle cx="100" cy="100" r="60" fill="rgba(255,255,255,0.1)" stroke="white" stroke-width="2"/>
    <circle cx="100" cy="100" r="30" fill="white"/>
    <path d="M85 85L115 100L85 115V85Z" fill="#8B5CF6"/>
    
    <!-- Main Title -->
    <text x="200" y="80" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="white">
      ReelVision Studio
    </text>
    
    <!-- Subtitle -->
    <text x="200" y="120" font-family="Arial, sans-serif" font-size="24" fill="rgba(255,255,255,0.9)">
      Professional Video Creation &amp; Editing Services
    </text>
    
    <!-- Features -->
    <g transform="translate(200, 160)">
      <text x="0" y="0" font-family="Arial, sans-serif" font-size="18" fill="rgba(255,255,255,0.8)">
        ✨ Instagram Reels • YouTube Shorts • TikTok Videos
      </text>
      <text x="0" y="30" font-family="Arial, sans-serif" font-size="18" fill="rgba(255,255,255,0.8)">
        🎬 5+ Years Experience • 500+ Happy Clients • Unlimited Revisions
      </text>
    </g>
    
    <!-- Call to Action -->
    <rect x="200" y="220" width="200" height="50" rx="25" fill="white"/>
    <text x="300" y="250" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#8B5CF6" text-anchor="middle">
      Start Your Project
    </text>
    
  </g>
  
  <!-- Decorative Elements -->
  <circle cx="1000" cy="150" r="80" fill="rgba(255,255,255,0.05)"/>
  <circle cx="1100" cy="400" r="60" fill="rgba(255,255,255,0.05)"/>
  <circle cx="950" cy="500" r="40" fill="rgba(255,255,255,0.05)"/>
  
  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#06B6D4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
