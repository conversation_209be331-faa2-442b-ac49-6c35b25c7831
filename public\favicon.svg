<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle with Gradient -->
  <circle cx="16" cy="16" r="16" fill="url(#gradient)" />
  
  <!-- Video Camera Icon -->
  <path d="M8 11C8 9.89543 8.89543 9 10 9H18C19.1046 9 20 9.89543 20 11V21C20 22.1046 19.1046 23 18 23H10C8.89543 23 8 22.1046 8 21V11Z" fill="white"/>
  
  <!-- Camera Lens -->
  <circle cx="14" cy="16" r="3" fill="url(#gradient)" stroke="white" stroke-width="0.5"/>
  
  <!-- Play <PERSON> in Lens -->
  <path d="M13 14.5L16 16L13 17.5V14.5Z" fill="white"/>
  
  <!-- Recording Light -->
  <circle cx="18.5" cy="12.5" r="1" fill="#ff4444"/>
  
  <!-- Film Strip Lines -->
  <rect x="21" y="13" width="3" height="1" fill="white" opacity="0.8"/>
  <rect x="21" y="15" width="3" height="1" fill="white" opacity="0.8"/>
  <rect x="21" y="17" width="3" height="1" fill="white" opacity="0.8"/>
  <rect x="21" y="19" width="3" height="1" fill="white" opacity="0.8"/>
  
  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
